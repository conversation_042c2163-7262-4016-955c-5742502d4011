---
openapi: 3.0.1
info:
  title: API V1
  version: v1
paths:
  "/api/v1/auth/login":
    post:
      summary: Login to get jwt key
      tags:
      - Authentication
      description: Authenticating a user
      parameters: []
      responses:
        '200':
          description: Authentication successful
          content:
            application/json:
              schema:
                type: object
                properties:
                  token:
                    type: string
                  expiry_time:
                    type: string
                  user:
                    type: object
                    properties:
                      id:
                        type: integer
                      username:
                        type: string
                      first_name:
                        type: string
                      middle_name:
                        type: string
                      last_name:
                        type: string
                      sex:
                        type: string
                      is_active:
                        type: boolean
                      date_of_birth:
                        type: string
                      birth_date_estimated:
                        type: string
                      voided:
                        type: integer
                      voided_reason:
                        type: string
                      roles:
                        type: array
                        items:
                          type: object
                          properties:
                            id:
                              type: integer
                            role_id:
                              type: integer
                            role_name:
                              type: string
                      departments:
                        type: array
                        items:
                          type: object
                          properties:
                            id:
                              type: integer
                            name:
                              type: string
                            retired:
                              type: integer
                            retired_reason:
                              type: string
        '401':
          description: UnAuthorized
          content:
            application/json:
              schema:
                type: object
                properties:
                  error:
                    type: string
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                username:
                  type: string
                password:
                  type: string
                department:
                  type: string
              required:
              - username
              - password
              - department
  "/api/v1/auth/application_login":
    post:
      summary: application login auth
      tags:
      - Authentication
      description: Authenticating a user
      parameters: []
      responses:
        '200':
          description: Authentication successful
          content:
            application/json:
              schema:
                type: object
                properties:
                  token:
                    type: string
                  expiry_time:
                    type: string
                  user:
                    type: object
                    properties:
                      id:
                        type: integer
                      username:
                        type: string
                      first_name:
                        type: string
                      middle_name:
                        type: string
                      last_name:
                        type: string
                      sex:
                        type: string
                      is_active:
                        type: boolean
                      date_of_birth:
                        type: string
                      birth_date_estimated:
                        type: string
                      voided:
                        type: integer
                      voided_reason:
                        type: string
                      roles:
                        type: array
                        items:
                          type: object
                          properties:
                            id:
                              type: integer
                            role_id:
                              type: integer
                            role_name:
                              type: string
                      departments:
                        type: array
                        items:
                          type: object
                          properties:
                            id:
                              type: integer
                            name:
                              type: string
                            retired:
                              type: integer
                            retired_reason:
                              type: string
        '401':
          description: UnAuthorized
          content:
            application/json:
              schema:
                type: object
                properties:
                  error:
                    type: string
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                username:
                  type: string
                password:
                  type: string
              required:
              - username
              - password
  "/api/v1/auth/refresh_token":
    get:
      summary: refresh_token auth
      tags:
      - Authentication
      description: Refresh token
      security:
      - bearerAuth: []
      responses:
        '200':
          description: Authentication successful
          content:
            application/json:
              schema:
                type: object
                properties:
                  token:
                    type: string
                  expiry_time:
                    type: string
                  user:
                    type: object
                    properties:
                      id:
                        type: integer
                      username:
                        type: string
                      first_name:
                        type: string
                      middle_name:
                        type: string
                      last_name:
                        type: string
                      sex:
                        type: string
                      is_active:
                        type: boolean
                      date_of_birth:
                        type: string
                      birth_date_estimated:
                        type: string
                      voided:
                        type: integer
                      voided_reason:
                        type: string
                      roles:
                        type: array
                        items:
                          type: object
                          properties:
                            id:
                              type: integer
                            role_id:
                              type: integer
                            role_name:
                              type: string
                      departments:
                        type: array
                        items:
                          type: object
                          properties:
                            id:
                              type: integer
                            name:
                              type: string
                            retired:
                              type: integer
                            retired_reason:
                              type: string
        '401':
          description: UnAuthorized
          content:
            application/json:
              schema:
                type: object
                properties:
                  error:
                    type: string
  "/api/v1/global":
    get:
      summary: Display site details
      tags:
      - Global
      description: Manage site details
      responses:
        '200':
          description: successful
          content:
            application/json:
              schema:
                type: object
                properties:
                  id:
                    type: integer
                  name:
                    type: string
                  code:
                    type: string
                  address:
                    type: string
                  phone:
                    type: string
                  created_date:
                    type: string
    post:
      summary: create site with details
      tags:
      - Global
      description: Manage site details
      security:
      - bearerAuth: []
      parameters: []
      responses:
        '200':
          description: successful
          content:
            application/json:
              schema:
                type: object
                properties:
                  id:
                    type: integer
                  name:
                    type: string
                  code:
                    type: string
                  address:
                    type: string
                  phone:
                    type: string
                  created_date:
                    type: string
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                name:
                  type: string
                code:
                  type: string
                address:
                  type: string
                phone:
                  type: string
              required:
              - name
              - code
              - address
              - phone
  "/api/v1/global/{id}":
    parameters:
    - name: id
      in: path
      description: id
      required: true
      schema:
        type: string
    put:
      summary: update site details
      tags:
      - Global
      description: Manage site details
      security:
      - bearerAuth: []
      parameters: []
      responses:
        '200':
          description: successful
          content:
            application/json:
              schema:
                type: object
                properties:
                  id:
                    type: integer
                  name:
                    type: string
                  code:
                    type: string
                  address:
                    type: string
                  phone:
                    type: string
                  created_date:
                    type: string
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                name:
                  type: string
                code:
                  type: string
                address:
                  type: string
                phone:
                  type: string
              required:
              - name
              - code
              - address
              - phone
    delete:
      summary: delete site details
      tags:
      - Global
      description: Manage site details
      security:
      - bearerAuth: []
      responses:
        '200':
          description: successful
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
  "/api/v1/global/current_api_tag":
    get:
      summary: Get Api Git Tag
      tags:
      - Global
      description: Manage site details
      responses:
        '200':
          description: successful
          content:
            application/json:
              schema:
                type: object
                properties:
                  git_tag:
                    type: string
  "/api/v1/interfacer/fetch_results":
    get:
      summary: fetch_results interfacer
      tags:
      - Interfacer
      description: Interfacer API Endpoints
      security:
      - bearerAuth: []
      parameters:
      - name: accession_number
        in: query
        description: accession_number
        schema:
          type: string
      responses:
        '200':
          description: successful
          content:
            application/json:
              schema:
                type: array
                items:
                  type: object
                  properties:
                    indicator_id:
                      type: string
                    value:
                      type: string
                    machine_name:
                      type: string
                    indicator_name:
                      type: string
  "/api/v1/interfacer/result_available":
    get:
      summary: result_available interfacer
      tags:
      - Interfacer
      description: Interfacer API Endpoints
      security:
      - bearerAuth: []
      parameters:
      - name: accession_number
        in: query
        description: accession_number
        schema:
          type: string
      responses:
        '200':
          description: successful
          content:
            application/json:
              schema:
                type: object
                properties:
                  result_available:
                    type: boolean
  "/api/v1/interfacer":
    post:
      summary: update interfacer
      tags:
      - Interfacer
      description: Interfacer API Endpoints
      parameters: []
      responses:
        '200':
          description: successful
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                accession_number:
                  type: string
                machine_name:
                  type: string
                measure_id:
                  type: string
                result:
                  type: string
                PHP_AUTH_USER:
                  type: string
                PHP_AUTH_PW:
                  type: string
              required:
              - accession_number
              - machine_name
              - measure_id
              - result
servers:
- url: http://{defaultHost}
  variables:
    defaultHost:
      default: localhost:3000
components:
  securitySchemes:
    bearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
