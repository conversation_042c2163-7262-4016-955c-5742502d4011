<!DOCTYPE html>
<html>
<head>
  <%= csrf_meta_tags %>
  <%= csp_meta_tag %>
  <%= javascript_include_tag 'vue' %>
  <%= stylesheet_link_tag 'font', media: 'all' %>
  <%= javascript_include_tag 'moment' %>
  <%= javascript_include_tag 'tailwind' %>
</head>
<body>
<div id="app" style="font-size: 10px">
  <div v-if="stockOrderData" class="px-5 print-container">
    <div class="py-5 px-5 space-y-3 print-container">
      <div class="rounded-tr rounded-tl px-5 py-5 flex flex-col items-center">
        <img src="/images/logo.png" alt="app-logo" class="w-24 h-24 object-cover" />
        <h3 class="mt-2 text-xl font-medium uppercase">Republic of Malawi</h3>
        <h3 class="mt-2 text-xl font-medium">Ministry of Health</h3>
        <h3 class="mt-2 text-2xl font-semibold">Requisition and Issue Voucher</h3>
      </div>
      
      <div class="flex bg-gray-50 border-l-4 border-l-100 rounded-r px-2 py-2 items-center space-x-2">
        <svg class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 5v2m0 4v2m0 4v2M5 5a2 2 0 00-2 2v3a2 2 0 110 4v3a2 2 0 002 2h14a2 2 0 002-2v-3a2 2 0 110-4V7a2 2 0 00-2-2H5z" />
        </svg>
        <p>Voucher Number: <span class="text-lg text-sky-500 font-medium">B</span><strong>{{ stockOrderData.voucher_number }}</strong></p>
      </div>
      
      <div class="mt-3">
        <table class="w-full">
          <thead class="w-full border-t border-l border-r">
            <tr class="border-b border-t border-r border-l rounded">
              <th colspan="4" class="text-left p-2">
                Requisitions
              </th>
            </tr>
            <tr class="border-b border-t border-r border-l rounded">
              <th class="px-2 py-2 text-left border-r">
                Stock Item
              </th>
              <th class="px-2 py-2 text-left border-r">
                Quantity Being Requested
              </th>
              <th class="px-2 py-2 text-left border-r">
                Quantity Issued
              </th>
              <th class="px-2 py-2 text-left">
                Quantity Collected
              </th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="(requisition, index) in requisitions" :key="index"
                class="border-b border-t border-r border-l rounded">
              <td class="px-2 py-2 border-r">{{ requisition.item.name }}</td>
              <td class="px-2 py-2 border-r">{{ requisition.quantity_requested }}</td>
              <td class="px-2 py-2 border-r">{{ requisition.quantity_issued }}</td>
              <td class="px-2 py-2">{{ requisition.quantity_collected }}</td>
            </tr>
          </tbody>
        </table>

        <!-- Preparation of RIV Section -->
        <div class="rounded border mt-5">
          <div class="flex items-center space-x-3 bg-gray-50 py-2 rounded-t px-2 border-b">
            <svg class="w-6 h-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.78 0-2.674-2.155-1.414-3.414l5-5A2 2 0 009 7.172V5L8 4z" />
            </svg>
            <h3 class="text-lg font-semibold">Preparation of RIV</h3>
          </div>
          <div class="w-full grid grid-cols-2 gap-5 py-5 px-5">
            <div class="col-span-1 flex flex-col space-y-2">
              <div class="w-full flex items-center space-x-2">
                <p class="w-72 font-medium">Prepared by: </p>
                <span class="w-full border-b-2 text-gray-700 border-dotted pb-1 font-normal border-gray-300">{{ preparedBy }}</span>
              </div>
              <div class="w-full flex items-center space-x-2">
                <p class="w-72 font-medium">Designation: </p>
                <span class="w-full border-b-2 text-gray-700 border-dotted pb-1 font-normal border-gray-300">{{ preparedDesignation }}</span>
              </div>
              <div class="w-full flex items-center space-x-2">
                <p class="w-72 font-medium">Signature: </p>
                <span class="w-full border-b-2 text-gray-700 border-dotted pb-1 font-normal border-gray-300">{{ preparedSignature }}</span>
              </div>
              <div class="w-full flex items-center space-x-2">
                <p class="w-72 font-medium">Date:</p>
                <span class="w-full border-b-2 text-gray-700 border-dotted pb-1 font-normal border-gray-300">{{ preparedDate }}</span>
              </div>
            </div>
            <div class="col-span-1 flex flex-col space-y-2">
              <div class="w-full flex items-center space-x-2">
                <p class="w-72 font-medium">Certified by: </p>
                <span class="w-full border-b-2 text-gray-700 border-dotted pb-1 font-normal border-gray-300">{{ certifiedBy }}</span>
              </div>
              <div class="w-full flex items-center space-x-2">
                <p class="w-72 font-medium">Designation: </p>
                <span class="w-full border-b-2 text-gray-700 border-dotted pb-1 font-normal border-gray-300">{{ certifiedDesignation }}</span>
              </div>
              <div class="w-full flex items-center space-x-2">
                <p class="w-72 font-medium">Signature: </p>
                <span class="w-full border-b-2 text-gray-700 border-dotted pb-1 font-normal border-gray-300">{{ certifiedSignature }}</span>
              </div>
              <div class="w-full flex items-center space-x-2">
                <p class="w-72 font-medium">Date:</p>
                <span class="w-full border-b-2 text-gray-700 border-dotted pb-1 font-normal border-gray-300">{{ certifiedDate }}</span>
              </div>
            </div>
          </div>
        </div>

        <!-- Pharmacy Section -->
        <div class="rounded border mt-5">
          <div class="flex items-center space-x-3 bg-gray-50 py-2 rounded-t px-2 border-b">
            <svg class="w-6 h-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
            </svg>
            <h3 class="text-lg font-semibold">Pharmacy</h3>
          </div>
          <div class="w-full grid grid-cols-2 gap-5 py-5 px-5">
            <div class="col-span-1 flex flex-col space-y-2">
              <div class="w-full flex items-center space-x-2">
                <p class="w-72 font-medium">Issued by: </p>
                <span class="w-full border-b-2 text-gray-700 border-dotted pb-1 font-normal border-gray-300">{{ issuerName }}</span>
              </div>
              <div class="w-full flex items-center space-x-2">
                <p class="w-72 font-medium">Designation: </p>
                <span class="w-full border-b-2 text-gray-700 border-dotted pb-1 font-normal border-gray-300">{{ issuerDesignation }}</span>
              </div>
              <div class="w-full flex items-center space-x-2">
                <p class="w-72 font-medium">Signature: </p>
                <span class="w-full border-b-2 text-gray-700 border-dotted pb-1 font-normal border-gray-300">{{ issuerSignature }}</span>
              </div>
              <div class="w-full flex items-center space-x-2">
                <p class="w-72 font-medium">Date:</p>
                <span class="w-full border-b-2 text-gray-700 border-dotted pb-1 font-normal border-gray-300">{{ issuedDate }}</span>
              </div>
            </div>
            <div class="col-span-1 flex flex-col space-y-2">
              <div class="w-full flex items-center space-x-2">
                <p class="w-72 font-medium">Approved by: </p>
                <span class="w-full border-b-2 text-gray-700 border-dotted pb-1 font-normal border-gray-300">{{ approverName }}</span>
              </div>
              <div class="w-full flex items-center space-x-2">
                <p class="w-72 font-medium">Designation: </p>
                <span class="w-full border-b-2 text-gray-700 border-dotted pb-1 font-normal border-gray-300">{{ approverDesignation }}</span>
              </div>
              <div class="w-full flex items-center space-x-2">
                <p class="w-72 font-medium">Signature: </p>
                <span class="w-full border-b-2 text-gray-700 border-dotted pb-1 font-normal border-gray-300">{{ approverSignature }}</span>
              </div>
              <div class="w-full flex items-center space-x-2">
                <p class="w-72 font-medium">Date:</p>
                <span class="w-full border-b-2 text-gray-700 border-dotted pb-1 font-normal border-gray-300">{{ approvedDate }}</span>
              </div>
            </div>
          </div>
        </div>

        <!-- Finalisation of Order Section -->
        <div class="rounded border mt-5">
          <div class="flex items-center space-x-3 bg-gray-50 py-2 rounded-t px-2 border-b">
            <svg class="w-6 h-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <h3 class="text-lg font-semibold">Finalisation of Order</h3>
          </div>
          <div class="w-full grid grid-cols-2 gap-5 py-5 px-5">
            <div class="col-span-1 flex flex-col space-y-2">
              <div class="w-full flex items-center space-x-2">
                <p class="w-72 font-medium">Collected by: </p>
                <span class="w-full border-b-2 text-gray-700 border-dotted pb-1 font-normal border-gray-300">{{ collectedBy }}</span>
              </div>
              <div class="w-full flex items-center space-x-2">
                <p class="w-72 font-medium">Designation: </p>
                <span class="w-full border-b-2 text-gray-700 border-dotted pb-1 font-normal border-gray-300">{{ collectedDesignation }}</span>
              </div>
              <div class="w-full flex items-center space-x-2">
                <p class="w-72 font-medium">Signature: </p>
                <span class="w-full border-b-2 text-gray-700 border-dotted pb-1 font-normal border-gray-300">{{ collectedSignature }}</span>
              </div>
              <div class="w-full flex items-center space-x-2">
                <p class="w-72 font-medium">Date:</p>
                <span class="w-full border-b-2 text-gray-700 border-dotted pb-1 font-normal border-gray-300">{{ collectedDate }}</span>
              </div>
            </div>
            <div class="col-span-1 flex flex-col space-y-2">
              <div class="w-full flex items-center space-x-2">
                <p class="w-72 font-medium">Verified by: </p>
                <span class="w-full border-b-2 text-gray-700 border-dotted pb-1 font-normal border-gray-300">{{ verifiedBy }}</span>
              </div>
              <div class="w-full flex items-center space-x-2">
                <p class="w-72 font-medium">Designation: </p>
                <span class="w-full border-b-2 text-gray-700 border-dotted pb-1 font-normal border-gray-300">{{ verifiedDesignation }}</span>
              </div>
              <div class="w-full flex items-center space-x-2">
                <p class="w-72 font-medium">Signature: </p>
                <span class="w-full border-b-2 text-gray-700 border-dotted pb-1 font-normal border-gray-300">{{ verifiedSignature }}</span>
              </div>
              <div class="w-full flex items-center space-x-2">
                <p class="w-72 font-medium">Date:</p>
                <span class="w-full border-b-2 text-gray-700 border-dotted pb-1 font-normal border-gray-300">{{ verifiedDate }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<script>
const { createApp, ref } = Vue

const app = createApp({
  data() {
    return {
      stockOrderData: {},
      requisitions: [],
      moment: moment,
      dateFormat: "DD/MMM/YYYY HH:mm",
      issuerName: '',
      issuerDesignation: '',
      issuerSignature: '',
      approverName: '',
      approverDesignation: '',
      approverSignature: '',
      issuedDate: '',
      approvedDate: '',
      preparedBy: '',
      preparedDesignation: '',
      preparedSignature: '',
      preparedDate: '',
      certifiedBy: '',
      certifiedDesignation: '',
      certifiedSignature: '',
      certifiedDate: '',
      collectedBy: '',
      collectedDesignation: '',
      collectedSignature: '',
      collectedDate: '',
      verifiedBy: '',
      verifiedDesignation: '',
      verifiedSignature: '',
      verifiedDate: ''
    };
  },
  methods: {
    processApproversAndIssuers(data) {
      data.forEach(item => {
        if (item.record_type === 'issuer') {
          this.issuerName = item.name;
          this.issuerDesignation = item.designation;
          this.issuerSignature = item.signature;
          this.issuedDate = moment(item.created_date).format(this.dateFormat);
        } else if (item.record_type === 'approver') {
          this.approverName = item.name;
          this.approverDesignation = item.designation;
          this.approverSignature = item.signature;
          this.approvedDate = moment(item.created_date).format(this.dateFormat);
        }
      });
    },
    processStatusTrail(trails) {
      trails.forEach(trail => {
        const statusName = trail.stock_status.name.toLowerCase();
        const initiatorName = `${trail.initiator.first_name} ${trail.initiator.last_name}`;
        const date = moment(trail.created_date).format(this.dateFormat);
        
        switch (statusName) {
          case 'draft':
            this.preparedBy = initiatorName;
            this.preparedDesignation = 'Laboratory';
            this.preparedSignature = trail.initiator.username;
            this.preparedDate = date;
            break;
          case 'requested':
            this.certifiedBy = initiatorName;
            this.certifiedDesignation = 'Laboratory';
            this.certifiedSignature = trail.initiator.username;
            this.certifiedDate = date;
            break;
          case 'received':
            this.collectedBy = initiatorName;
            this.collectedDesignation = 'Laboratory';
            this.collectedSignature = trail.initiator.username;
            this.collectedDate = date;
            break;
          case 'approved':
            this.verifiedBy = initiatorName;
            this.verifiedDesignation = 'Laboratory';
            this.verifiedSignature = trail.initiator.username;
            this.verifiedDate = date;
            break;
        }
      });
    }
  },
  mounted() {
    // Replace this with the actual data injection from Rails controller
    let data = <%= raw @stock_order.to_json %>;
    
    this.stockOrderData = data;
    this.requisitions = data.stock_requisitions || [];
    
    if (data.stock_pharmacy_approver_and_issuers) {
      this.processApproversAndIssuers(data.stock_pharmacy_approver_and_issuers);
    }
    
    if (data.stock_order_status_trail) {
      this.processStatusTrail(data.stock_order_status_trail);
    }
  }
}).mount('#app')
</script>
</body>
</html>