class Order < VoidableRecord
  belongs_to :encounter
  belongs_to :priority
  has_many :tests
  has_many :order_statuses
  has_many :client_order_print_trails

  after_create :create_default_status
  after_create :create_unsync_order

  def create_default_status
    OrderStatus.create!(order_id: id, status_id: Status.find_by_name('specimen-not-collected').id,
                        creator: User.current.id)
  end

  def as_json(options = {})
    specimen_test_type = specimen_test_type()
    super(options).merge(
      {
        client_id: encounter.as_json['client_id'],
        client: encounter.as_json['client'],
        client_history: encounter.client_history,
        specimen: specimen_test_type[:specimen],
        test_types: specimen_test_type[:test_types],
        order_status:,
        print_count:,
        order_status_trail:,
        request_origin:,
        requesting_ward:,
        tests: order_tests
      }
    ).as_json
  end

  def order_tests
    tests.as_json({ client_report: true })
  end

  def specimen_test_type
    specimen = []
    test_types = []
    tests.each do |test|
      specimen.push(test.specimen.name)
      test_t = test.test_type&.short_name.blank? ? test.test_type&.name : test.test_type&.short_name
      test_types.push({
                        name: test_t,
                        department: test.test_type&.department&.name
                      })
    end
    {
      test_types:,
      specimen: specimen.uniq.join(', ')
    }
  end

  def order_status
    OrderStatus.where(order_id: id).order(created_date: :desc)&.first&.status&.name
  end

  def order_status_trail
    OrderStatus.where(order_id: id)
  end

  def request_origin
    encounter_type = EncounterType.find_by(id: encounter.encounter_type_id)
    encounter_type.nil? ? '' : encounter_type.name
  end

  def print_count
    client_order_print_trails.count
  end

  def requesting_ward
    ward = encounter.facility_section
    ward.nil? ? '' : ward.name
  end

  def create_unsync_order
    UnsyncOrder.create(
      test_or_order_id: id,
      data_not_synced: 'new order',
      data_level: 'order',
      sync_status: 0
    )
  end

  def self.requesting_clinician(name)
    where("requested_by LIKE '#{name}%'").select('distinct requested_by').limit(10).map(&:requested_by)
  end
end
