class Api::V1::SpecimenTestTypeMappingsController < ApplicationController
  before_action :set_specimen_test_type_mapping, only: %i[show update destroy]

  def index
    search = params[:search] || nil
    @specimen_test_type_mappings = paginate(SpecimenTestTypeMapping.joins(:specimen, :test_type)
                                            .select('specimen_test_type_mappings.id,
                                                    specimen_test_type_mappings.test_type_id,
                                                    specimen_test_type_mappings.specimen_id,
                                                    specimen_test_type_mappings.life_span,
                                                    specimen_test_type_mappings.life_span_units,
                                                    specimen.name AS specimen_name,
                                                    test_types.name AS test_type_name')
                                            .where('specimen.name LIKE ? OR test_types.name LIKE ?
                                                    OR specimen_test_type_mappings.life_span LIKE ?',
                                                   "%#{search}%", "%#{search}%", "%#{search}%")
                                                    .where.not("test_types.name LIKE '%(Paed%'")
                                            .where.not("test_types.name LIKE '%(cancer%'"))
    render json: @specimen_test_type_mappings
  end

  def show
    render json: @specimen_test_type_mapping, include: [specimen: { only: :name }, test_type: { only: :name }]
  end

  def create
    @specimen_test_type_mapping = SpecimenTestTypeMapping.new(specimen_test_type_mapping_params)

    if @specimen_test_type_mapping.save
      render json: @specimen_test_type_mapping, status: :created, location: [:api, :v1, @specimen_test_type_mapping]
    else
      render json: @specimen_test_type_mapping.errors, status: :unprocessable_entity
    end
  end

  def update
    if @specimen_test_type_mapping.update(specimen_test_type_mapping_params)
      render json: @specimen_test_type_mapping, include: [specimen: { only: :name }, test_type: { only: :name }]
    else
      render json: @specimen_test_type_mapping.errors, status: :unprocessable_entity
    end
  end

  def destroy
    @specimen_test_type_mapping.destroy
  end

  private

  def set_specimen_test_type_mapping
    @specimen_test_type_mapping = SpecimenTestTypeMapping.find(params[:id])
  end

  def specimen_test_type_mapping_params
    params.require(%i[life_span life_span_units])
    params.require(:specimen_test_type_mapping).permit(:search, :life_span, :life_span_units, :specimen_id,
                                                       :test_type_id, :retired, :retired_by, :retired_reason, :retired_date, :creator, :updated_date, :created_date)
  end
end
