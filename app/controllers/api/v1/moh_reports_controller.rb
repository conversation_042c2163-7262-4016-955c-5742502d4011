# frozen_string_literal: true

# API V1 module for MoH Report Controller
module Api
  module V1
    # Controller that handles all requests pertaining to MoH Reports
    class MohReportsController < ApplicationController
      before_action :report_params
      skip_before_action :report_params, only: %i[report_indicators]

      def report_indicators
        department = params.require(:department)
        render json: Reports::MohService.report_indicators(department)
      end

      def haematology
        data = Reports::ReportCacheService.find(@report_id)
        data ||= Reports::ReportCacheService.create(
          Reports::MohService.generate_haematology_report(@year)
        )
        render json: data
      end

      def blood_bank
        data = Reports::ReportCacheService.find(@report_id)
        data ||= Reports::ReportCacheService.create(
          Reports::MohService.generate_blood_bank_report(@year)
        )
        render json: data
      end

      def biochemistry
        data = Reports::ReportCacheService.find(@report_id)
        data ||= Reports::ReportCacheService.create(
          Reports::MohService.generate_biochemistry_report(@year)
        )
        render json: data
      end

      def parasitology
        data = Reports::ReportCacheService.find(@report_id)
        data ||= Reports::ReportCacheService.create(
          Reports::MohService.generate_parasitology_report(@year)
        )
        render json: data
      end

      def microbiology
        data = Reports::ReportCacheService.find(@report_id)
        data ||= Reports::ReportCacheService.create(
          Reports::MohService.generate_microbiology_report(@year)
        )
        render json: data
      end

      def serology
        data = Reports::ReportCacheService.find(@report_id)
        data ||= Reports::ReportCacheService.create(
          Reports::MohService.generate_serology_report(@year)
        )
        render json: data
      end

      private

      def report_params
        @year = params.require(:year)
        @report_id = params[:report_id]
      end
    end
  end
end
