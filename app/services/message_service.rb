# frozen_string_literal: true

# module MessageService
module MessageService
  RECORD_NOT_FOUND = 'Record not found'
  RECORD_DELETED = 'Record deleted successfully'
  VALUE_NOT_ARRAY = 'OR value is not an array'
  RECORD_ACTIVATED = 'Record activated successfully'
  MISSING_REQUIRED_PARAMETERS = 'Missing required parameters'
  STOCK_ORDER_APPROVED = 'Stock Order Approved'
  STOCK_ORDER_REJECTED = 'Stock Order Rejected'
  STOCK_REQUISITION_REJECTED = 'Stock Requisition Rejected'
  RECORD_CREATED = 'Record created successfully'
  STOCK_REQUISITION_RECEIVED = 'Stock Requisition Received'
  STOCK_REQUISITION_APPROVED = 'Stock Requisition Approved'
  STOCK_REQUISITION_NOT_COLLECTED = 'Stock Requisition Not Collected'
  STOCK_ORDER_RECEIVED = 'Stock Order Received'
end
