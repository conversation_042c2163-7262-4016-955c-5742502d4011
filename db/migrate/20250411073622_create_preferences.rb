class CreatePreferences < ActiveRecord::Migration[7.0]
  def change
    return if table_exists?(:preferences)

    create_table :preferences do |t|
      t.string :name
      t.json :value
      t.integer :voided
      t.bigint :voided_by
      t.string :voided_reason
      t.datetime :voided_date
      t.bigint :creator
      t.datetime :created_date, null: false
      t.datetime :updated_date, null: false
      t.bigint :updated_by, null: true
    end
    add_foreign_key :preferences, :users, column: :creator, primary_key: :id
    add_foreign_key :preferences, :users, column: :updated_by, primary_key: :id
    add_foreign_key :preferences, :users, column: :voided_by, primary_key: :id
    add_index :preferences, :name, unique: true
  end
end
