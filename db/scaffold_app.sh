#!/bin/bash

# Scaffolding app
rails generate api_scaffold Person first_name:string middle_name:string last_name:string sex:string date_of_birth:date birth_date_estimated:integer voided:integer voided_by:bigint voided_reason:string voided_date:datetime creator:bigint created_date:datetime updated_date:bigint &&
rails generate api_scaffold Client person:references uuid:binary voided:integer voided_by:bigint voided_reason:string voided_date:datetime creator:bigint created_date:datetime updated_date:bigint &&
rails generate api_scaffold ClientIdentifierType name:string retired:integer retired_by:bigint retired_reason:string retired_date:datetime creator:bigint created_date:datetime updated_date:datetime &&
rails generate api_scaffold ClientIdentifier client_identifier_type:references value:string client:references voided:integer voided_by:bigint voided_reason:string voided_date:datetime creator:bigint created_date:datetime updated_date:bigint uuid:string &&
rails generate api_scaffold Role name:string retired:integer retired_by:bigint retired_reason:string retired_date:datetime creator:bigint updated_date:datetime created_date:datetime &&
rails generate api_scaffold Privilege name:bigint retired:integer retired_by:bigint retired_reason:string retired_date:datetime creator:bigint updated_date:datetime created_date:datetime &&
rails generate api_scaffold RolePrivilegeMapping role:references privilege:references voided:integer voided_by:bigint voided_reason:string voided_date:datetime creator:bigint created_date:datetime updated_date:bigint &&
rails generate api_scaffold Priority name:string retired:integer retired_by:bigint retired_reason:string retired_date:datetime creator:bigint updated_date:datetime created_date:datetime &&
rails generate api_scaffold Organism name:string description:string retired:integer retired_by:bigint retired_reason:string retired_date:datetime creator:bigint updated_date:datetime created_date:datetime &&
rails generate api_scaffold Drug short_name:string name:string retired:integer retired_by:bigint retired_reason:string retired_date:datetime creator:bigint updated_date:datetime created_date:datetime &&
rails generate api_scaffold DrugOrganismMapping drug:references organism:references retired:integer retired_by:bigint retired_reason:string retired_date:datetime creator:bigint updated_date:datetime created_date:datetime &&
rails generate api_scaffold TestPanel name:string description:string retired:integer retired_by:bigint retired_reason:string retired_date:datetime creator:bigint updated_date:datetime created_date:datetime &&
rails generate api_scaffold Instrument name:string description:string ip_address:string hostname:string retired:integer retired_by:bigint retired_reason:string retired_date:datetime creator:bigint created_date:datetime updated_date:datetime &&
rails generate api_scaffold StatusReason description:string retired:integer retired_by:bigint retired_reason:string retired_date:datetime creator:bigint updated_date:datetime created_date:datetime &&
rails generate api_scaffold Facility name:string retired:integer retired_by:bigint retired_reason:string retired_date:datetime creator:bigint created_date:datetime updated_date:datetime &&
rails generate api_scaffold FacilitySection name:string retired:integer retired_by:bigint retired_reason:string retired_date:datetime creator:bigint created_date:datetime updated_date:datetime &&
rails generate api_scaffold User role:references person:references username:string password:string last_password_changed:string voided:integer voided_by:bigint voided_reason:string voided_date:datetime creator:bigint created_date:datetime updated_date:bigint &&
rails generate api_scaffold Department name:string retired:integer retired_by:bigint retired_reason:string retired_date:datetime creator:bigint updated_date:datetime created_date:datetime &&
rails generate api_scaffold UserDepartmentMapping user:references department:references retired:integer retired_by:bigint retired_reason:string retired_date:datetime creator:bigint updated_date:datetime created_date:datetime &&
rails generate api_scaffold Status name:string retired:integer retired_by:bigint retired_reason:string retired_date:datetime creator:bigint updated_date:datetime updated_date_copy1:datetime &&
rails generate api_scaffold Specimen name:string retired:integer retired_by:bigint retired_reason:string retired_date:datetime creator:bigint created_date:datetime updated_date:datetime  --force-plural &&
rails generate api_scaffold TestType name:string department:references expected_turn_around_time:decimal retired:integer retired_by:bigint retired_reason:string retired_date:datetime creator:bigint updated_date:datetime created_date:datetime &&
rails generate api_scaffold TestTypePanelMapping test_type:references test_panel:references voided:integer voided_by:bigint voided_reason:string voided_date:datetime creator:bigint created_date:datetime updated_date:datetime &&
rails generate api_scaffold SpecimenTestTypeMapping specimen:references test_type:references retired:integer retired_by:bigint retired_reason:string retired_date:datetime creator:bigint updated_date:datetime created_date:datetime &&
rails generate api_scaffold TestIndicator name:string test_type:references test_indicator_type:integer unit:string description:string retired:integer retired_by:bigint retired_reason:string retired_date:datetime creator:bigint created_date:datetime updated_date:datetime &&
rails generate api_scaffold TestIndicatorRange test_indicator:references min_age:integer max_age:integer sex:string lower_range:bigint upper_range:bigint interpretation:string value:string retired:integer retired_by:bigint retired_reason:string retired_date:datetime creator:bigint created_date:datetime updated_date:datetime &&
rails generate api_scaffold InstrumentTestTypeMapping instrument:references test_type:references retired:integer retired_by:bigint retired_reason:string retired_date:datetime creator:bigint created_date:datetime updated_date:datetime &&
rails generate api_scaffold Encounter client:references facility:references destination:references facility_section:references start_date:datetime end_date:datetime voided:integer voided_by:bigint voided_reason:string voided_date:datetime creator:bigint created_date:datetime updated_date:datetime uuid:string &&
rails generate api_scaffold Order encounter:references priority:references accession_number:string tracking_number:string requested_by:string sample_collected_time:datetime collected_by:string creator:bigint voided:integer voided_by:bigint voided_reason:string voided_date:datetime &&
rails generate api_scaffold Test specimen:references order:references test_type:references voided:integer voided_by:bigint voided_reason:string voided_date:datetime creator:bigint created_date:datetime updated_date:datetime &&
rails generate api_scaffold TestStatus test:references status:references status_reason:references creator:bigint voided:integer voided_by:bigint voided_reason:string voided_date:datetime &&
rails generate api_scaffold TestResult test:references test_indicator:references value:text result_date:datetime voided:integer voided_by:bigint voided_reason:string voided_date:datetime creator:bigint created_date:datetime updated_date:datetime &&
rails generate api_scaffold DrugSusceptibilityTestResult drug:references organism:references zone:integer interpretation:column test:references voided:integer voided_by:bigint voided_reason:string voided_date:datetime creator:bigint created_date:datetime updated_date:datetime &&
rails generate api_scaffold CultureObservation test:references description:text observation_datetime:datetime voided:integer voided_by:bigint voided_reason:string voided_date:datetime creator:bigint created_date:datetime updated_date:datetime &&
rails generate api_scaffold ClientOrderPrintTrail order:references creator:bigint voided:integer voided_by:bigint voided_reason:string voided_date:datetime
