-- MySQL dump 10.13  Distrib 8.0.33, for Linux (x86_64)
--
-- Host: 127.0.0.1    Database: mlab_mo
-- ------------------------------------------------------
-- Server version	8.0.33

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!50503 SET NAMES utf8mb4 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Table structure for table `departments`
--

DROP TABLE IF EXISTS `departments`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `departments` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `name` varchar(255) DEFAULT NULL,
  `retired` int DEFAULT NULL,
  `retired_by` bigint DEFAULT NULL,
  `retired_reason` varchar(255) DEFAULT NULL,
  `retired_date` datetime(6) DEFAULT NULL,
  `creator` bigint DEFAULT NULL,
  `updated_date` datetime(6) DEFAULT NULL,
  `created_date` datetime(6) DEFAULT NULL,
  `updated_by` bigint DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `fk_rails_07e4d2e8c0` (`retired_by`),
  KEY `fk_rails_7aef731b14` (`creator`),
  CONSTRAINT `fk_rails_07e4d2e8c0` FOREIGN KEY (`retired_by`) REFERENCES `users` (`id`),
  CONSTRAINT `fk_rails_7aef731b14` FOREIGN KEY (`creator`) REFERENCES `users` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=15 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `departments`
--

LOCK TABLES `departments` WRITE;
/*!40000 ALTER TABLE `departments` DISABLE KEYS */;
INSERT INTO `departments` VALUES (1,'Parasitology',0,NULL,NULL,NULL,1,'2024-05-02 13:55:56.994923','2024-05-02 13:55:56.994923',NULL),(2,'Microbiology',0,NULL,NULL,NULL,1,'2024-05-02 13:55:56.998849','2024-05-02 13:55:56.998849',NULL),(3,'Haematology',0,NULL,NULL,NULL,1,'2024-05-02 13:55:57.006382','2024-05-02 13:55:57.006382',NULL),(4,'Serology',0,NULL,NULL,NULL,1,'2024-05-02 13:55:57.012686','2024-05-02 13:55:57.012686',NULL),(5,'Blood Bank',0,NULL,NULL,NULL,1,'2024-05-02 13:55:57.017766','2024-05-02 13:55:57.017766',NULL),(6,'Lab Reception',0,NULL,NULL,NULL,1,'2024-05-02 13:55:57.023512','2024-05-02 13:55:57.023512',NULL),(7,'Biochemistry',0,NULL,NULL,NULL,1,'2024-05-02 13:55:57.027040','2024-05-02 13:55:57.027040',NULL),(8,'Flow Cytometry',0,NULL,NULL,NULL,1,'2024-05-02 13:55:57.031033','2024-05-02 13:55:57.031033',NULL),(9,'DNA/PCR',0,NULL,NULL,NULL,1,'2024-05-02 13:55:57.039036','2024-05-02 13:55:57.039036',NULL),(10,'Immunochemistry',0,NULL,NULL,NULL,1,'2024-05-02 13:55:57.044502','2024-05-02 13:55:57.044502',NULL),(11,'Paediatric Lab ',1,NULL,'No longer needed','2024-05-02 14:11:24.989835',1,'2024-05-02 14:11:24.990021','2024-05-02 13:55:57.050559',NULL),(12,'Archives',0,NULL,NULL,NULL,1,'2024-05-02 13:55:57.055712','2024-05-02 13:55:57.055712',NULL),(13,'Cancer-Haematology',1,NULL,'No longer needed','2024-05-02 14:11:24.995051',1,'2024-05-02 14:11:24.995135','2024-05-02 13:55:57.059984',NULL),(14,'Cancer-Biochemistry',1,NULL,'No longer needed','2024-05-02 14:11:24.998729',1,'2024-05-02 14:11:24.998795','2024-05-02 13:55:57.063524',NULL);
/*!40000 ALTER TABLE `departments` ENABLE KEYS */;
UNLOCK TABLES;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2024-05-03  8:25:01
