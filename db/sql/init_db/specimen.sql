-- MySQL dump 10.13  Distrib 8.0.33, for Linux (x86_64)
--
-- Host: 127.0.0.1    Database: mlab_mo
-- ------------------------------------------------------
-- Server version	8.0.33

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!50503 SET NAMES utf8mb4 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Table structure for table `specimen`
--

DROP TABLE IF EXISTS `specimen`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `specimen` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `name` varchar(255) DEFAULT NULL,
  `description` varchar(255) DEFAULT NULL,
  `retired` int DEFAULT NULL,
  `retired_by` bigint DEFAULT NULL,
  `retired_reason` varchar(255) DEFAULT NULL,
  `retired_date` datetime(6) DEFAULT NULL,
  `creator` bigint DEFAULT NULL,
  `created_date` datetime(6) DEFAULT NULL,
  `updated_date` datetime(6) DEFAULT NULL,
  `updated_by` bigint DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `fk_rails_10ee122152` (`retired_by`),
  KEY `fk_rails_3d6fc047ad` (`creator`),
  CONSTRAINT `fk_rails_10ee122152` FOREIGN KEY (`retired_by`) REFERENCES `users` (`id`),
  CONSTRAINT `fk_rails_3d6fc047ad` FOREIGN KEY (`creator`) REFERENCES `users` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=30 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `specimen`
--

LOCK TABLES `specimen` WRITE;
/*!40000 ALTER TABLE `specimen` DISABLE KEYS */;
INSERT INTO `specimen` VALUES (1,'Sputum','',0,NULL,NULL,NULL,1,'2024-05-02 14:10:49.007987','2024-05-02 14:10:49.007987',1),(2,'CSF','',0,NULL,NULL,NULL,1,'2024-05-02 14:10:49.014029','2024-05-02 14:10:49.014029',1),(3,'Blood','',0,NULL,NULL,NULL,1,'2024-05-02 14:10:49.018590','2024-05-02 14:10:49.018590',1),(4,'Pleural Fluid','',0,NULL,NULL,NULL,1,'2024-05-02 14:10:49.023441','2024-05-02 14:10:49.023441',1),(5,'Ascitic Fluid','',0,NULL,NULL,NULL,1,'2024-05-02 14:10:49.027362','2024-05-02 14:10:49.027362',1),(6,'Pericardial Fluid','',0,NULL,NULL,NULL,1,'2024-05-02 14:10:49.030918','2024-05-02 14:10:49.030918',1),(7,'Peritoneal Fluid','',0,NULL,NULL,NULL,1,'2024-05-02 14:10:49.034625','2024-05-02 14:10:49.034625',1),(8,'HVS','',0,NULL,NULL,NULL,1,'2024-05-02 14:10:49.039211','2024-05-02 14:10:49.039211',1),(9,'Swabs','',0,NULL,NULL,NULL,1,'2024-05-02 14:10:49.045622','2024-05-02 14:10:49.045622',1),(10,'Pus','',0,NULL,NULL,NULL,1,'2024-05-02 14:10:49.051422','2024-05-02 14:10:49.051422',1),(11,'Stool','',0,NULL,NULL,NULL,1,'2024-05-02 14:10:49.060396','2024-05-02 14:10:49.060396',1),(12,'Urine','',0,NULL,NULL,NULL,1,'2024-05-02 14:10:49.080973','2024-05-02 14:10:49.080973',1),(13,'Other','',0,NULL,NULL,NULL,1,'2024-05-02 14:10:49.111920','2024-05-02 14:10:49.111920',1),(15,'Semen','',0,NULL,NULL,NULL,1,'2024-05-02 14:10:49.116830','2024-05-02 14:10:49.116830',1),(16,'Swab','',0,NULL,NULL,NULL,1,'2024-05-02 14:10:49.121709','2024-05-02 14:10:49.121709',1),(17,'Synovial Fluid','',0,NULL,NULL,NULL,1,'2024-05-02 14:10:49.125621','2024-05-02 14:10:49.125621',1),(18,'Plasma','',0,NULL,NULL,NULL,1,'2024-05-02 14:10:49.128991','2024-05-02 14:10:49.128991',1),(19,'DBS (Free drop to DBS card)','',0,NULL,NULL,NULL,1,'2024-05-02 14:10:49.132384','2024-05-02 14:10:49.132384',1),(20,'DBS','',0,NULL,NULL,NULL,1,'2024-05-02 14:10:49.135797','2024-05-02 14:10:49.135797',1),(21,'DBS (Using capillary tube)','',0,NULL,NULL,NULL,1,'2024-05-02 14:10:49.141198','2024-05-02 14:10:49.141198',1),(22,' tissue biopsy','',0,NULL,NULL,NULL,1,'2024-05-02 14:10:49.146088','2024-05-02 14:10:49.146088',1),(23,'Gastric aspirate','',0,NULL,NULL,NULL,1,'2024-05-02 14:10:49.150183','2024-05-02 14:10:49.150183',1),(24,'Nasopharyngeal swab','',0,NULL,NULL,NULL,1,'2024-05-02 14:10:49.155155','2024-05-02 14:10:49.155155',1),(25,'Fluid Aspirate','',0,NULL,NULL,NULL,1,'2024-05-02 14:10:49.158991','2024-05-02 14:10:49.158991',1),(26,'DBS 70ml','',0,NULL,NULL,NULL,1,'2024-05-02 14:10:49.163058','2024-05-02 14:10:49.163058',1),(27,'Serum','',0,NULL,NULL,NULL,1,'2024-05-02 14:10:49.166910','2024-05-02 14:10:49.166910',1),(28,'Dialysis Water','',0,NULL,NULL,NULL,1,'2024-05-02 14:10:49.170419','2024-05-02 14:10:49.170419',1),(29,'Nosal swab','',0,NULL,NULL,NULL,1,'2024-05-02 14:10:49.176991','2024-05-02 14:10:49.176991',1);
/*!40000 ALTER TABLE `specimen` ENABLE KEYS */;
UNLOCK TABLES;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2024-05-03  8:25:02
