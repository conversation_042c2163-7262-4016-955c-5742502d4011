-- MySQL dump 10.13  Distrib 8.0.33, for Linux (x86_64)
--
-- Host: 127.0.0.1    Database: mlab_mo
-- ------------------------------------------------------
-- Server version	8.0.33

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!50503 SET NAMES utf8mb4 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Table structure for table `roles`
--

DROP TABLE IF EXISTS `roles`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `roles` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `name` varchar(255) DEFAULT NULL,
  `retired` int DEFAULT NULL,
  `retired_by` bigint DEFAULT NULL,
  `retired_reason` varchar(255) DEFAULT NULL,
  `retired_date` datetime(6) DEFAULT NULL,
  `creator` bigint DEFAULT NULL,
  `updated_date` datetime(6) DEFAULT NULL,
  `created_date` datetime(6) DEFAULT NULL,
  `updated_by` bigint DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `fk_rails_aa96010497` (`retired_by`),
  KEY `fk_rails_f0b260e680` (`creator`),
  CONSTRAINT `fk_rails_aa96010497` FOREIGN KEY (`retired_by`) REFERENCES `users` (`id`),
  CONSTRAINT `fk_rails_f0b260e680` FOREIGN KEY (`creator`) REFERENCES `users` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=11 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `roles`
--

LOCK TABLES `roles` WRITE;
/*!40000 ALTER TABLE `roles` DISABLE KEYS */;
INSERT INTO `roles` VALUES (1,'Superadmin',0,NULL,NULL,NULL,1,'2024-05-02 13:55:44.968794','2024-05-02 13:55:44.968794',NULL),(2,'Technologist',0,NULL,NULL,NULL,1,'2024-05-02 13:55:44.972253','2024-05-02 13:55:44.972253',NULL),(3,'Receptionist',0,NULL,NULL,NULL,1,'2024-05-02 13:55:44.983021','2024-05-02 13:55:44.983021',NULL),(4,'Supervisor',0,NULL,NULL,NULL,1,'2024-05-02 13:55:44.986225','2024-05-02 13:55:44.986225',NULL),(5,'Technician',0,NULL,NULL,NULL,1,'2024-05-02 13:55:44.989697','2024-05-02 13:55:44.989697',NULL),(6,'Lab Assistant',0,NULL,NULL,NULL,1,'2024-05-02 13:55:44.992920','2024-05-02 13:55:44.992920',NULL),(7,'Lab Manager ',0,NULL,NULL,NULL,1,'2024-05-02 13:55:44.996054','2024-05-02 13:55:44.996054',NULL),(8,'Superuser',0,NULL,NULL,NULL,1,'2024-05-02 13:55:44.999433','2024-05-02 13:55:44.999433',NULL),(9,'LIMS Secreteriat',0,NULL,NULL,NULL,1,'2024-05-02 13:55:45.006128','2024-05-02 13:55:45.006128',NULL),(10,'lab attendant',0,NULL,NULL,NULL,1,'2024-05-02 13:55:45.011296','2024-05-02 13:55:45.011296',NULL);
/*!40000 ALTER TABLE `roles` ENABLE KEYS */;
UNLOCK TABLES;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2024-05-03  8:25:01
