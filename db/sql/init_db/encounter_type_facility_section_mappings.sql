-- MySQL dump 10.13  Distrib 8.0.33, for Linux (x86_64)
--
-- Host: 127.0.0.1    Database: mlab_mo
-- ------------------------------------------------------
-- Server version	8.0.33

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!50503 SET NAMES utf8mb4 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Table structure for table `encounter_type_facility_section_mappings`
--

DROP TABLE IF EXISTS `encounter_type_facility_section_mappings`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `encounter_type_facility_section_mappings` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `creator` bigint DEFAULT NULL,
  `voided` int DEFAULT NULL,
  `voided_by` bigint DEFAULT NULL,
  `voided_reason` varchar(255) DEFAULT NULL,
  `voided_date` datetime(6) DEFAULT NULL,
  `created_date` datetime(6) DEFAULT NULL,
  `updated_date` datetime(6) DEFAULT NULL,
  `facility_section_id` bigint NOT NULL,
  `encounter_type_id` bigint NOT NULL,
  `updated_by` bigint DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `fk_rails_afdb26e1db` (`facility_section_id`),
  KEY `fk_rails_2880469aa4` (`encounter_type_id`),
  KEY `fk_rails_f4b4388678` (`voided_by`),
  KEY `fk_rails_0a2ba9e596` (`creator`),
  CONSTRAINT `fk_rails_0a2ba9e596` FOREIGN KEY (`creator`) REFERENCES `users` (`id`),
  CONSTRAINT `fk_rails_2880469aa4` FOREIGN KEY (`encounter_type_id`) REFERENCES `encounter_types` (`id`),
  CONSTRAINT `fk_rails_afdb26e1db` FOREIGN KEY (`facility_section_id`) REFERENCES `facility_sections` (`id`),
  CONSTRAINT `fk_rails_f4b4388678` FOREIGN KEY (`voided_by`) REFERENCES `users` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=58 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `encounter_type_facility_section_mappings`
--

LOCK TABLES `encounter_type_facility_section_mappings` WRITE;
/*!40000 ALTER TABLE `encounter_type_facility_section_mappings` DISABLE KEYS */;
INSERT INTO `encounter_type_facility_section_mappings` VALUES (1,1,0,NULL,NULL,NULL,'2024-05-02 00:00:00.000000','2024-05-02 14:11:19.659872',5,15,1),(2,1,0,NULL,NULL,NULL,'2024-05-02 00:00:00.000000','2024-05-02 14:11:19.666965',29,13,1),(3,1,0,NULL,NULL,NULL,'2024-05-02 00:00:00.000000','2024-05-02 14:11:19.676928',30,13,1),(4,1,0,NULL,NULL,NULL,'2024-05-02 00:00:00.000000','2024-05-02 14:11:19.686415',41,13,1),(5,1,0,NULL,NULL,NULL,'2024-05-02 00:00:00.000000','2024-05-02 14:11:19.694724',25,13,1),(6,1,0,NULL,NULL,NULL,'2024-05-02 00:00:00.000000','2024-05-02 14:11:19.704767',35,13,1),(7,1,0,NULL,NULL,NULL,'2024-05-02 00:00:00.000000','2024-05-02 14:11:19.716783',61,13,1),(8,1,0,NULL,NULL,NULL,'2024-05-02 00:00:00.000000','2024-05-02 14:11:19.726444',32,13,1),(9,1,0,NULL,NULL,NULL,'2024-05-02 00:00:00.000000','2024-05-02 14:11:19.734164',26,13,1),(10,1,0,NULL,NULL,NULL,'2024-05-02 00:00:00.000000','2024-05-02 14:11:19.744994',16,13,1),(11,1,0,NULL,NULL,NULL,'2024-05-02 00:00:00.000000','2024-05-02 14:11:19.754438',6,13,1),(12,1,0,NULL,NULL,NULL,'2024-05-02 00:00:00.000000','2024-05-02 14:11:19.762717',4,13,1),(13,1,0,NULL,NULL,NULL,'2024-05-02 00:00:00.000000','2024-05-02 14:11:19.769921',62,13,1),(14,1,0,NULL,NULL,NULL,'2024-05-02 00:00:00.000000','2024-05-02 14:11:19.781369',23,13,1),(15,1,0,NULL,NULL,NULL,'2024-05-02 00:00:00.000000','2024-05-02 14:11:19.792201',27,13,1),(16,1,0,NULL,NULL,NULL,'2024-05-02 00:00:00.000000','2024-05-02 14:11:19.801172',12,2,1),(17,1,0,NULL,NULL,NULL,'2024-05-02 00:00:00.000000','2024-05-02 14:11:19.812291',13,2,1),(18,1,0,NULL,NULL,NULL,'2024-05-02 00:00:00.000000','2024-05-02 14:11:19.822173',14,2,1),(19,1,0,NULL,NULL,NULL,'2024-05-02 00:00:00.000000','2024-05-02 14:11:19.829950',15,2,1),(20,1,0,NULL,NULL,NULL,'2024-05-02 00:00:00.000000','2024-05-02 14:11:19.837820',17,2,1),(21,1,0,NULL,NULL,NULL,'2024-05-02 00:00:00.000000','2024-05-02 14:11:19.849522',18,2,1),(22,1,0,NULL,NULL,NULL,'2024-05-02 00:00:00.000000','2024-05-02 14:11:19.859147',19,2,1),(23,1,0,NULL,NULL,NULL,'2024-05-02 00:00:00.000000','2024-05-02 14:11:19.866255',20,2,1),(24,1,0,NULL,NULL,NULL,'2024-05-02 00:00:00.000000','2024-05-02 14:11:19.876811',33,2,1),(25,1,0,NULL,NULL,NULL,'2024-05-02 00:00:00.000000','2024-05-02 14:11:19.886576',64,2,1),(26,1,0,NULL,NULL,NULL,'2024-05-02 00:00:00.000000','2024-05-02 14:11:19.896649',34,2,1),(27,1,0,NULL,NULL,NULL,'2024-05-02 00:00:00.000000','2024-05-02 14:11:19.905580',24,2,1),(28,1,0,NULL,NULL,NULL,'2024-05-02 00:00:00.000000','2024-05-02 14:11:19.917313',2,2,1),(29,1,0,NULL,NULL,NULL,'2024-05-02 00:00:00.000000','2024-05-02 14:11:19.926270',71,2,1),(30,1,0,NULL,NULL,NULL,'2024-05-02 00:00:00.000000','2024-05-02 14:11:19.933821',22,2,1),(31,1,0,NULL,NULL,NULL,'2024-05-02 00:00:00.000000','2024-05-02 14:11:19.944496',7,2,1),(32,1,0,NULL,NULL,NULL,'2024-05-02 00:00:00.000000','2024-05-02 14:11:19.953953',21,2,1),(33,1,0,NULL,NULL,NULL,'2024-05-02 00:00:00.000000','2024-05-02 14:11:19.962365',3,2,1),(34,1,0,NULL,NULL,NULL,'2024-05-02 00:00:00.000000','2024-05-02 14:11:19.970229',1,2,1),(35,1,0,NULL,NULL,NULL,'2024-05-02 00:00:00.000000','2024-05-02 14:11:19.982635',25,2,1),(36,1,0,NULL,NULL,NULL,'2024-05-02 00:00:00.000000','2024-05-02 14:11:19.992568',10,2,1),(37,1,0,NULL,NULL,NULL,'2024-05-02 00:00:00.000000','2024-05-02 14:11:20.000666',37,2,1),(38,1,0,NULL,NULL,NULL,'2024-05-02 00:00:00.000000','2024-05-02 14:11:20.012476',36,2,1),(39,1,0,NULL,NULL,NULL,'2024-05-02 00:00:00.000000','2024-05-02 14:11:20.027380',39,2,1),(40,1,0,NULL,NULL,NULL,'2024-05-02 00:00:00.000000','2024-05-02 14:11:20.034728',38,2,1),(41,1,0,NULL,NULL,NULL,'2024-05-02 00:00:00.000000','2024-05-02 14:11:20.046494',59,2,1),(42,1,0,NULL,NULL,NULL,'2024-05-02 00:00:00.000000','2024-05-02 14:11:20.057015',40,2,1),(43,1,0,NULL,NULL,NULL,'2024-05-02 00:00:00.000000','2024-05-02 14:11:20.065641',45,2,1),(44,1,0,NULL,NULL,NULL,'2024-05-02 00:00:00.000000','2024-05-02 14:11:20.076558',11,2,1),(45,1,0,NULL,NULL,NULL,'2024-05-02 00:00:00.000000','2024-05-02 14:11:20.088741',66,2,1),(46,1,0,NULL,NULL,NULL,'2024-05-02 00:00:00.000000','2024-05-02 14:11:20.097996',70,2,1),(47,1,0,NULL,NULL,NULL,'2024-05-02 00:00:00.000000','2024-05-02 14:11:20.108539',56,2,1),(48,1,0,NULL,NULL,NULL,'2024-05-02 00:00:00.000000','2024-05-02 14:11:20.119416',31,2,1),(49,1,0,NULL,NULL,NULL,'2024-05-02 00:00:00.000000','2024-05-02 14:11:20.128551',44,2,1),(50,1,0,NULL,NULL,NULL,'2024-05-02 00:00:00.000000','2024-05-02 14:11:20.136325',16,2,1),(51,1,0,NULL,NULL,NULL,'2024-05-02 00:00:00.000000','2024-05-02 14:11:20.148103',43,2,1),(52,1,0,NULL,NULL,NULL,'2024-05-02 00:00:00.000000','2024-05-02 14:11:20.157290',67,2,1),(53,1,0,NULL,NULL,NULL,'2024-05-02 00:00:00.000000','2024-05-02 14:11:20.165139',68,2,1),(54,1,0,NULL,NULL,NULL,'2024-05-02 00:00:00.000000','2024-05-02 14:11:20.175228',58,2,1),(55,1,0,NULL,NULL,NULL,'2024-05-02 00:00:00.000000','2024-05-02 14:11:20.186632',42,2,1),(56,1,0,NULL,NULL,NULL,'2024-05-02 00:00:00.000000','2024-05-02 14:11:20.196399',57,2,1),(57,1,0,NULL,NULL,NULL,'2024-05-02 00:00:00.000000','2024-05-02 14:11:20.204332',9,2,1);
/*!40000 ALTER TABLE `encounter_type_facility_section_mappings` ENABLE KEYS */;
UNLOCK TABLES;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2024-05-03  8:25:01
