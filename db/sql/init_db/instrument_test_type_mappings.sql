-- MySQL dump 10.13  Distrib 8.0.33, for Linux (x86_64)
--
-- Host: 127.0.0.1    Database: mlab_mo
-- ------------------------------------------------------
-- Server version	8.0.33

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!50503 SET NAMES utf8mb4 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Table structure for table `instrument_test_type_mappings`
--

DROP TABLE IF EXISTS `instrument_test_type_mappings`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `instrument_test_type_mappings` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `instrument_id` bigint NOT NULL,
  `test_type_id` bigint NOT NULL,
  `retired` int DEFAULT NULL,
  `retired_by` bigint DEFAULT NULL,
  `retired_reason` varchar(255) DEFAULT NULL,
  `retired_date` datetime(6) DEFAULT NULL,
  `creator` bigint DEFAULT NULL,
  `created_date` datetime(6) DEFAULT NULL,
  `updated_date` datetime(6) DEFAULT NULL,
  `updated_by` bigint DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `index_instrument_test_type_mappings_on_instrument_id` (`instrument_id`),
  KEY `index_instrument_test_type_mappings_on_test_type_id` (`test_type_id`),
  KEY `fk_rails_051201364f` (`retired_by`),
  KEY `fk_rails_d97721fc8d` (`creator`),
  CONSTRAINT `fk_rails_051201364f` FOREIGN KEY (`retired_by`) REFERENCES `users` (`id`),
  CONSTRAINT `fk_rails_0533d8ec58` FOREIGN KEY (`instrument_id`) REFERENCES `instruments` (`id`),
  CONSTRAINT `fk_rails_2f76c50eb2` FOREIGN KEY (`test_type_id`) REFERENCES `test_types` (`id`),
  CONSTRAINT `fk_rails_d97721fc8d` FOREIGN KEY (`creator`) REFERENCES `users` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=49 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `instrument_test_type_mappings`
--

LOCK TABLES `instrument_test_type_mappings` WRITE;
/*!40000 ALTER TABLE `instrument_test_type_mappings` DISABLE KEYS */;
INSERT INTO `instrument_test_type_mappings` VALUES (1,4,32,0,NULL,NULL,NULL,NULL,NULL,NULL,NULL),(2,4,33,0,NULL,NULL,NULL,NULL,NULL,NULL,NULL),(3,4,34,0,NULL,NULL,NULL,NULL,NULL,NULL,NULL),(4,4,38,0,NULL,NULL,NULL,NULL,NULL,NULL,NULL),(5,4,46,0,NULL,NULL,NULL,NULL,NULL,NULL,NULL),(6,4,63,0,NULL,NULL,NULL,NULL,NULL,NULL,NULL),(7,4,64,0,NULL,NULL,NULL,NULL,NULL,NULL,NULL),(8,4,65,0,NULL,NULL,NULL,NULL,NULL,NULL,NULL),(9,4,66,0,NULL,NULL,NULL,NULL,NULL,NULL,NULL),(10,4,67,0,NULL,NULL,NULL,NULL,NULL,NULL,NULL),(11,8,10,0,NULL,NULL,NULL,NULL,NULL,NULL,NULL),(12,10,36,0,NULL,NULL,NULL,NULL,NULL,NULL,NULL),(14,20,35,0,NULL,NULL,NULL,NULL,NULL,NULL,NULL),(15,21,35,0,NULL,NULL,NULL,NULL,NULL,NULL,NULL),(16,26,32,0,NULL,NULL,NULL,NULL,NULL,NULL,NULL),(17,26,33,0,NULL,NULL,NULL,NULL,NULL,NULL,NULL),(18,26,34,0,NULL,NULL,NULL,NULL,NULL,NULL,NULL),(19,26,36,0,NULL,NULL,NULL,NULL,NULL,NULL,NULL),(20,26,38,0,NULL,NULL,NULL,NULL,NULL,NULL,NULL),(21,26,48,0,NULL,NULL,NULL,NULL,NULL,NULL,NULL),(22,26,49,0,NULL,NULL,NULL,NULL,NULL,NULL,NULL),(23,26,50,0,NULL,NULL,NULL,NULL,NULL,NULL,NULL),(24,26,52,0,NULL,NULL,NULL,NULL,NULL,NULL,NULL),(31,32,32,0,NULL,NULL,NULL,NULL,NULL,NULL,NULL),(32,32,33,0,NULL,NULL,NULL,NULL,NULL,NULL,NULL),(33,32,34,0,NULL,NULL,NULL,NULL,NULL,NULL,NULL),(34,32,36,0,NULL,NULL,NULL,NULL,NULL,NULL,NULL),(35,32,38,0,NULL,NULL,NULL,NULL,NULL,NULL,NULL),(36,32,48,0,NULL,NULL,NULL,NULL,NULL,NULL,NULL),(37,32,49,0,NULL,NULL,NULL,NULL,NULL,NULL,NULL),(38,32,50,0,NULL,NULL,NULL,NULL,NULL,NULL,NULL),(39,32,52,0,NULL,NULL,NULL,NULL,NULL,NULL,NULL);
/*!40000 ALTER TABLE `instrument_test_type_mappings` ENABLE KEYS */;
UNLOCK TABLES;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2024-05-03  8:25:01
