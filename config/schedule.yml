moh_report_job:
  name: "Update Moh Report Job at 23:55"
  cron: "55 23 * * *"
  class: "MohReportJob"
  queue: default

# nlims_sync_job:
#   name: "Sync with Nlim<PERSON> Every 5mins"
#   cron: "*/5 * * * *"
#   class: "NlimsSyncJob"
#   queue: default

home_dashboard_job:
  name: "Update Home Dashboard analytics Every 5mins"
  cron: "*/5 * * * *"
  class: "HomeDashboardJob"
  queue: default

# update_elasticsearch_job:
#   name: "Update elastic search every 2mins"
#   cron: "*/2 * * * *"
#   class: "UpdateElasticsearchIndexJob"
#   queue: default

oerr_sync_job:
  name: "Sync with oerr Every 20mins"
  cron: "*/20 * * * *"
  class: "OerrSyncJob"
  queue: default